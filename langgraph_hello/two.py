from typing import Annotated
from typing_extensions import TypedDict
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages

# Example 1: WITHOUT Annotated (overwrites)
# class StateWithoutAnnotated(TypedDict):
#     messages: list  # No reducer

# graph_builder = StateGraph(StateWithoutAnnotated)

# def node1(state):
#     return {"messages": ["Hello"]}

# def node2(state):
#     return {"messages": ["World"]}

# graph_builder.add_node("node1", node1)
# graph_builder.add_node("node2", node2)
# graph_builder.add_edge(START, "node1")
# graph_builder.add_edge("node1", "node2")
# graph_builder.add_edge("node2", END)

# graph = graph_builder.compile()
# result = graph.invoke({"messages": []})
# print(result["messages"])
# Output: ["World"]  <- Only the last message!


# # Example 2: WITH Annotated (appends)
class StateWithAnnotated(TypedDict):
    messages: Annotated[list, add_messages]  # Uses reducer

graph_builder2 = StateGraph(StateWithAnnotated)

def node1_append(state):
    return {"messages": ["Hello"]}

def node2_append(state):
    return {"messages": ["World"]}

graph_builder2.add_node("node1", node1_append)
graph_builder2.add_node("node2", node2_append)
graph_builder2.add_edge(START, "node1")
graph_builder2.add_edge("node1", "node2")
graph_builder2.add_edge("node2", END)

graph2 = graph_builder2.compile()
result2 = graph2.invoke({"messages": []})
print(result2["messages"])