import streamlit as st
import os
import pandas as pd
from datetime import datetime
from scheduler import get_scheduler
import uuid

# Page config
st.set_page_config(
    page_title="Script Scheduler",
    page_icon="⏰",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize scheduler using session state
if 'scheduler' not in st.session_state:
    st.session_state.scheduler = get_scheduler()
scheduler = st.session_state.scheduler

# Sidebar navigation
st.sidebar.title("Script Scheduler ⏰")
page = st.sidebar.selectbox(
    "Navigate to:",
    ["Add New Job", "Manage Jobs", "View Logs"]
)

# Helper functions
def validate_script_path(path):
    if not os.path.exists(path):
        return False, "File does not exist"
    if not os.path.isfile(path):
        return False, "Path is not a file"
    if not os.access(path, os.R_OK):
        return False, "File is not readable"
    return True, "File is valid"

def format_cron_description(cron_expr):
    descriptions = scheduler.get_predefined_schedules()
    for desc, expr in descriptions.items():
        if expr == cron_expr:
            return desc
    return f"Custom: {cron_expr}"

# Main content based on page selection
if page == "Add New Job":
    st.title("Add New Scheduled Job")
    
    with st.form("add_job_form"):
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.subheader("Script Configuration")
            
            # Script selection method
            script_method = st.radio(
                "How would you like to select the script?",
                ["Enter file path", "Browse and select"]
            )
            
            if script_method == "Enter file path":
                script_path = st.text_input(
                    "Script Path",
                    placeholder="/path/to/your/script.sh",
                    help="Enter the full path to your script file"
                )
            else:
                # Simple file browser - list files in current directory
                current_dir = os.getcwd()
                st.write(f"Current directory: `{current_dir}`")
                
                files = []
                try:
                    for item in os.listdir(current_dir):
                        if os.path.isfile(os.path.join(current_dir, item)):
                            files.append(item)
                except:
                    st.error("Unable to read current directory")
                
                if files:
                    selected_file = st.selectbox("Select a file:", [""] + sorted(files))
                    script_path = os.path.join(current_dir, selected_file) if selected_file else ""
                else:
                    st.warning("No files found in current directory")
                    script_path = ""
            
            # Validate script path
            if script_path:
                is_valid, message = validate_script_path(script_path)
                if is_valid:
                    st.success(f"✓ {message}")
                else:
                    st.error(f"✗ {message}")
            
            # Job description
            description = st.text_input(
                "Job Description (optional)",
                placeholder="Brief description of what this job does"
            )
        
        with col2:
            st.subheader("Schedule Configuration")
            
            # Schedule type
            schedule_type = st.radio(
                "Schedule Type",
                ["Predefined", "Custom Cron"]
            )
            
            if schedule_type == "Predefined":
                predefined_schedules = scheduler.get_predefined_schedules()
                selected_schedule = st.selectbox(
                    "Select Schedule",
                    list(predefined_schedules.keys())
                )
                cron_expression = predefined_schedules[selected_schedule]
                st.code(f"Cron: {cron_expression}")
            else:
                cron_expression = st.text_input(
                    "Cron Expression",
                    placeholder="*/5 * * * *",
                    help="Format: minute hour day month day_of_week"
                )
                st.caption("Example: `*/5 * * * *` runs every 5 minutes")
        
        # Submit button
        submitted = st.form_submit_button("Add Job", type="primary")
        
        if submitted:
            if not script_path:
                st.error("Please select a script file")
            elif not cron_expression:
                st.error("Please specify a schedule")
            else:
                is_valid, message = validate_script_path(script_path)
                if not is_valid:
                    st.error(f"Script validation failed: {message}")
                else:
                    # Generate unique job ID
                    job_id = f"job_{uuid.uuid4().hex[:8]}"
                    
                    # Add job to scheduler
                    success, result_message = scheduler.add_job(
                        job_id, script_path, cron_expression, description
                    )
                    
                    if success:
                        st.success(f"✓ Job added successfully! Job ID: {job_id}")
                        st.info(f"Schedule: {format_cron_description(cron_expression)}")
                    else:
                        st.error(f"Failed to add job: {result_message}")

elif page == "Manage Jobs":
    st.title("Manage Scheduled Jobs")
    
    # Get all jobs
    jobs = scheduler.get_all_jobs()
    
    if not jobs:
        st.info("No scheduled jobs found. Add a job from the 'Add New Job' page.")
    else:
        # Create DataFrame for better display
        job_data = []
        for job in jobs:
            job_data.append({
                "Job ID": job[0],
                "Script": os.path.basename(job[1]),
                "Full Path": job[1],
                "Schedule": format_cron_description(job[2]),
                "Description": job[3] or "No description",
                "Created": job[4],
                "Last Run": job[5] or "Never",
                "Next Run": job[6].strftime("%Y-%m-%d %H:%M:%S") if job[6] else "Not scheduled",
                "Status": job[7]
            })
        
        df = pd.DataFrame(job_data)
        
        # Display jobs in an interactive table
        st.subheader(f"Active Jobs ({len(jobs)})")
        
        # Job management actions
        for i, job in enumerate(job_data):
            with st.expander(f"📄 {job['Script']} - {job['Schedule']}", expanded=False):
                col1, col2, col3, col4 = st.columns([2, 1, 1, 1])
                
                with col1:
                    st.write(f"**Job ID:** `{job['Job ID']}`")
                    st.write(f"**Full Path:** `{job['Full Path']}`")
                    st.write(f"**Description:** {job['Description']}")
                    st.write(f"**Created:** {job['Created']}")
                    st.write(f"**Last Run:** {job['Last Run']}")
                    st.write(f"**Next Run:** {job['Next Run']}")
                
                with col2:
                    if st.button("▶️ Run Now", key=f"run_{job['Job ID']}", type="primary"):
                        success, message = scheduler.run_job_now(job['Job ID'], job['Full Path'])
                        if success:
                            st.success("Job started! Check logs for results.")
                        else:
                            st.error(f"Failed to run job: {message}")
                
                with col3:
                    if st.button("View Logs", key=f"logs_{job['Job ID']}"):
                        st.session_state[f"show_logs_{job['Job ID']}"] = True
                
                with col4:
                    if st.button("🗑️ Delete", key=f"delete_{job['Job ID']}", type="secondary"):
                        success, message = scheduler.remove_job(job['Job ID'])
                        if success:
                            st.success("Job deleted successfully!")
                            st.rerun()
                        else:
                            st.error(f"Failed to delete job: {message}")
                
                # Show logs if requested
                if st.session_state.get(f"show_logs_{job['Job ID']}", False):
                    logs = scheduler.get_job_logs(job['Job ID'], limit=10)
                    if logs:
                        st.subheader("Recent Execution Logs")
                        for log in logs:
                            timestamp, output, error_output, exit_code, duration = log
                            
                            log_color = "🟢" if exit_code == 0 else "🔴"
                            st.write(f"{log_color} **{timestamp}** (Exit code: {exit_code}, Duration: {duration:.2f}s)")
                            
                            if output:
                                st.code(output, language="text")
                            if error_output:
                                st.error(f"Error: {error_output}")
                            st.divider()
                    else:
                        st.info("No execution logs found for this job.")
                    
                    if st.button("Hide Logs", key=f"hide_logs_{job['Job ID']}"):
                        st.session_state[f"show_logs_{job['Job ID']}"] = False
                        st.rerun()

elif page == "View Logs":
    st.title("Execution Logs")
    
    jobs = scheduler.get_all_jobs()
    if not jobs:
        st.info("No jobs available.")
    else:
        # Select job to view logs
        job_options = {f"{job[0]} - {os.path.basename(job[1])}": job[0] for job in jobs}
        selected_job_display = st.selectbox("Select job to view logs:", list(job_options.keys()))
        
        if selected_job_display:
            selected_job_id = job_options[selected_job_display]
            
            # Number of logs to show
            log_limit = st.slider("Number of recent logs to show:", 5, 100, 20)
            
            logs = scheduler.get_job_logs(selected_job_id, limit=log_limit)
            
            if logs:
                st.subheader(f"Recent Execution Logs for {selected_job_display}")
                
                for i, log in enumerate(logs):
                    timestamp, output, error_output, exit_code, duration = log
                    
                    # Status indicator
                    status_color = "🟢" if exit_code == 0 else "🔴"
                    status_text = "Success" if exit_code == 0 else "Failed"
                    
                    with st.expander(f"{status_color} {timestamp} - {status_text} (Duration: {duration:.2f}s)"):
                        if output:
                            st.subheader("Output:")
                            st.code(output, language="text")
                        
                        if error_output:
                            st.subheader("Error Output:")
                            st.code(error_output, language="text")
                        
                        st.write(f"**Exit Code:** {exit_code}")
                        st.write(f"**Duration:** {duration:.2f} seconds")
            else:
                st.info("No execution logs found for this job.")

# Footer
st.sidebar.markdown("---")
st.sidebar.markdown("**Tips:**")
st.sidebar.markdown("• Scripts can be .py, .sh, or executable files")
st.sidebar.markdown("• Jobs persist between app restarts")
st.sidebar.markdown("• Check logs to monitor execution")

# Auto-refresh option
if st.sidebar.checkbox("Auto-refresh (30s)", value=False):
    import time
    time.sleep(30)
    st.rerun()