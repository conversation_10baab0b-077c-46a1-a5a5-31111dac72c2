from datetime import datetime
from langchain.chat_models import init_chat_model
from langchain.tools import tool
from langgraph.graph import StateGraph, MessagesState
from langgraph.prebuilt import ToolNode
from typing import Annotated
from typing_extensions import TypedDict
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode
from langchain.chat_models import init_chat_model
from langgraph.checkpoint.memory import MemorySaver
import os

class State(TypedDict):
    messages: Annotated[list, add_messages]
    
@tool
def add_numbers(a: int, b: int) -> int:
    """Add two numbers and return the result."""
    return a + b

@tool
def get_current_time() -> str:
    """Returns the current time in UTC."""
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")

@tool
def get_weather(city: str) -> str:
    """Get the weather for a given city.
    
    Args:
        city: The name of the city
        
    Returns:
        Weather information as a string
    """
    # In a real app, you'd call a weather API
    # For now, we'll return mock data
    weather_data = {
        "san francisco": "Sunny, 65°F",
        "new york": "Cloudy, 55°F",
        "london": "Rainy, 50°F"
    }
    return weather_data.get(city.lower(), f"Weather data not available for {city}")

@tool
def search_web(query: str) -> str:
    """Search the web for information.
    
    Args:
        query: The search query
        
    Returns:
        Search results as a string
    """
    # Mock implementation
    return f"Here are search results for '{query}': [Mock results]"

# minimal model init helper used in some docs
from langchain.chat_models import init_chat_model
model = init_chat_model(model="llama3.1:latest", model_provider="ollama")
model_with_tools = model.bind_tools([get_weather, search_web, get_current_time, add_numbers])

def chatbot(state: State):
    """Process messages and decide whether to use tools"""
    return {"messages": [model_with_tools.invoke(state["messages"])]}


# build graph
graph_builder = StateGraph(State)
graph_builder.add_node("chatbot", chatbot)
graph_builder.add_node("tools", ToolNode(tools=[get_weather, search_web, get_current_time, add_numbers]))    

def should_continue(state: State) -> str:
    """Determine if we should use tools or end"""
    messages = state["messages"]
    last_message = messages[-1]
    
    # If the LLM makes a tool call, route to tools
    # Otherwise, end the conversation
    if hasattr(last_message, "tool_calls") and last_message.tool_calls:
        return "tools"
    return END

graph_builder.add_edge(START, "chatbot")
graph_builder.add_conditional_edges("chatbot", should_continue)
graph_builder.add_edge("tools", "chatbot")  # After tools, go back to chatbot

memory = MemorySaver()

graph = graph_builder.compile(checkpointer=memory)

# Step 9: Test it
print("=== Test 1: Simple question (no tool needed) ===")
response1 = graph.invoke({
    "messages": [{"role": "user", "content": "What is 2+2?"}]
})
print(response1["messages"][-1].content)

print("\n=== Test 2: Weather question (uses tool) ===")
response2 = graph.invoke({
    "messages": [{"role": "user", "content": "What's the weather in San Francisco?"}]
})
print(response2["messages"][-1].content)

print("\n=== Test 3: Multiple questions ===")
response3 = graph.invoke({
    "messages": [{"role": "user", "content": "What's the weather in London and New York?"}]
})
print(response3["messages"][-1].content)

print("=== Conversation 1 (Thread 1) ===")
config1 = {"configurable": {"thread_id": "conversation-1"}}

response1 = graph.invoke(
    {"messages": [{"role": "user", "content": "Hi, my name is Alice"}]},
    config1
)
print("Bot:", response1["messages"][-1].content)

# Continue the same conversation
response2 = graph.invoke(
    {"messages": [{"role": "user", "content": "What's my name?"}]},
    config1
)
print("Bot:", response2["messages"][-1].content)

print("\n=== Conversation 2 (Thread 2) ===")
config2 = {"configurable": {"thread_id": "conversation-2"}}

response3 = graph.invoke(
    {"messages": [{"role": "user", "content": "Hi, my name is Bob"}]},
    config2
)
print("Bot:", response3["messages"][-1].content)

response4 = graph.invoke(
    {"messages": [{"role": "user", "content": "What's my name?"}]},
    config2
)
print("Bot:", response4["messages"][-1].content)

print("\n=== Back to Conversation 1 ===")
response5 = graph.invoke(
    {"messages": [{"role": "user", "content": "Do you remember my name?"}]},
    config1
)
print("Bot:", response5["messages"][-1].content)