import sqlite3
import subprocess
import logging
import os
import threading
from datetime import datetime
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.executors.pool import ThreadPoolExecutor

def execute_script_standalone(job_id, script_path, db_path="scheduler.db"):
    """Standalone function to execute scripts - can be serialized by APScheduler"""
    start_time = datetime.now()
    
    try:
        # Determine script type and execute accordingly
        if script_path.endswith('.py'):
            result = subprocess.run(['python3', script_path], 
                                  capture_output=True, text=True, timeout=30000)
        elif script_path.endswith('.sh'):
            result = subprocess.run(['bash', script_path], 
                                  capture_output=True, text=True, timeout=30000)
        else:
            # Try to execute as is
            result = subprocess.run([script_path], 
                                  capture_output=True, text=True, timeout=30000)
        
        duration = (datetime.now() - start_time).total_seconds()
        
        # Log execution results
        log_execution(job_id, result.stdout, result.stderr, 
                     result.returncode, duration, db_path)
        
        # Update last run time
        update_job_last_run(job_id, db_path)
        
    except subprocess.TimeoutExpired:
        duration = (datetime.now() - start_time).total_seconds()
        log_execution(job_id, "", "Script execution timed out (5 minutes)", 
                     -1, duration, db_path)
    except Exception as e:
        duration = (datetime.now() - start_time).total_seconds()
        log_execution(job_id, "", f"Error executing script: {str(e)}", 
                     -1, duration, db_path)

def log_execution(job_id, output, error_output, exit_code, duration, db_path):
    """Standalone function to log script execution"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT INTO job_logs (job_id, output, error_output, exit_code, duration_seconds)
        VALUES (?, ?, ?, ?, ?)
    ''', (job_id, output, error_output, exit_code, duration))
    
    conn.commit()
    conn.close()

def update_job_last_run(job_id, db_path):
    """Standalone function to update job last run time"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute('''
        UPDATE job_metadata 
        SET last_run = CURRENT_TIMESTAMP 
        WHERE job_id = ?
    ''', (job_id,))
    
    conn.commit()
    conn.close()

class ScriptScheduler:
    def __init__(self, db_path="scheduler.db"):
        self.db_path = db_path
        self.init_database()
        
        # Configure APScheduler
        jobstores = {
            'default': SQLAlchemyJobStore(url=f'sqlite:///{db_path}')
        }
        executors = {
            'default': ThreadPoolExecutor(20)
        }
        job_defaults = {
            'coalesce': False,
            'max_instances': 3
        }
        
        self.scheduler = BackgroundScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='UTC'
        )
        
        if not self.scheduler.running:
            self.scheduler.start()
    
    def init_database(self):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS job_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                job_id TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                output TEXT,
                error_output TEXT,
                exit_code INTEGER,
                duration_seconds REAL
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS job_metadata (
                job_id TEXT PRIMARY KEY,
                script_path TEXT NOT NULL,
                cron_expression TEXT NOT NULL,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_run DATETIME,
                next_run DATETIME,
                status TEXT DEFAULT 'active'
            )
        ''')
        
        conn.commit()
        conn.close()
    
    
    def add_job(self, job_id, script_path, cron_expression, description=""):
        try:
            # Parse cron expression
            cron_parts = cron_expression.split()
            if len(cron_parts) == 5:
                minute, hour, day, month, day_of_week = cron_parts
                
                # Add job to scheduler using standalone function
                self.scheduler.add_job(
                    func=execute_script_standalone,
                    args=[job_id, script_path, self.db_path],
                    trigger='cron',
                    minute=minute,
                    hour=hour,
                    day=day,
                    month=month,
                    day_of_week=day_of_week,
                    id=job_id
                )
                
                # Store job metadata
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # Get next run time
                job = self.scheduler.get_job(job_id)
                next_run = job.next_run_time if job else None
                
                cursor.execute('''
                    INSERT OR REPLACE INTO job_metadata 
                    (job_id, script_path, cron_expression, description, next_run)
                    VALUES (?, ?, ?, ?, ?)
                ''', (job_id, script_path, cron_expression, description, next_run))
                
                conn.commit()
                conn.close()
                
                return True, "Job added successfully"
            else:
                return False, "Invalid cron expression. Use format: minute hour day month day_of_week"
                
        except Exception as e:
            return False, f"Error adding job: {str(e)}"
    
    def remove_job(self, job_id):
        try:
            self.scheduler.remove_job(job_id)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('UPDATE job_metadata SET status = "deleted" WHERE job_id = ?', (job_id,))
            conn.commit()
            conn.close()
            
            return True, "Job removed successfully"
        except Exception as e:
            return False, f"Error removing job: {str(e)}"
    
    def get_all_jobs(self):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT job_id, script_path, cron_expression, description, 
                   created_at, last_run, next_run, status
            FROM job_metadata 
            WHERE status != 'deleted'
            ORDER BY created_at DESC
        ''')
        
        jobs = cursor.fetchall()
        conn.close()
        
        # Update next_run times from scheduler
        updated_jobs = []
        for job in jobs:
            job_id = job[0]
            scheduler_job = self.scheduler.get_job(job_id)
            next_run = scheduler_job.next_run_time if scheduler_job else None
            
            updated_job = list(job)
            updated_job[6] = next_run  # Update next_run
            updated_jobs.append(updated_job)
        
        return updated_jobs
    
    def get_job_logs(self, job_id, limit=50):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT timestamp, output, error_output, exit_code, duration_seconds
            FROM job_logs 
            WHERE job_id = ?
            ORDER BY timestamp DESC
            LIMIT ?
        ''', (job_id, limit))
        
        logs = cursor.fetchall()
        conn.close()
        return logs
    
    def get_predefined_schedules(self):
        return {
            "Every minute": "* * * * *",
            "Every 5 minutes": "*/5 * * * *", 
            "Every 15 minutes": "*/15 * * * *",
            "Every 30 minutes": "*/30 * * * *",
            "Every hour": "0 * * * *",
            "Every 2 hours": "0 */2 * * *",
            "Every 6 hours": "0 */6 * * *",
            "Daily at 9 AM": "0 9 * * *",
            "Daily at midnight": "0 0 * * *",
            "Weekly (Sunday at 2 AM)": "0 2 * * 0",
            "Monthly (1st at 3 AM)": "0 3 1 * *"
        }
    
    def run_job_now(self, job_id, script_path):
        """Manually execute a job immediately in a separate thread"""
        def run_in_thread():
            execute_script_standalone(job_id, script_path, self.db_path)
        
        thread = threading.Thread(target=run_in_thread)
        thread.daemon = True
        thread.start()
        return True, "Job execution started"
    
    def shutdown(self):
        if self.scheduler.running:
            self.scheduler.shutdown()

# Global scheduler instance
def get_scheduler():
    return ScriptScheduler()