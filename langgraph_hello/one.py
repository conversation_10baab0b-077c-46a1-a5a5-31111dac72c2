from typing import Annotated
from typing_extensions import TypedDict
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_community.chat_models import ChatOllama
import os

# Set your API key
os.environ["ANTHROPIC_API_KEY"] = "your-api-key-here"

# Step 1: Define the State
class State(TypedDict):
    # Messages list with add_messages reducer function
    # This appends new messages instead of overwriting
    messages: Annotated[list, add_messages]

# Step 2: Create the graph builder
graph_builder = StateGraph(State)

# Step 3: Initialize the LLM
llm = ChatOllama(model="llama3.1:latest")

# Step 4: Define a node function
def chatbot(state: State):
    """Node that processes messages using the LLM"""
    return {"messages": [llm.invoke(state["messages"])]}

# Step 5: Add the node to the graph
graph_builder.add_node("chatbot", chatbot)

# Step 6: Define the flow - Start -> chatbot -> End
graph_builder.add_edge(START, "chatbot")
graph_builder.add_edge("chatbot", END)

# Step 7: Compile the graph
graph = graph_builder.compile()

# Step 8: Run the chatbot
response = graph.invoke({
    "messages": [{"role": "user", "content": "Hello! What is LangGraph?"}]
})

print(response["messages"][-1].content)