from datetime import datetime
from langchain.chat_models import init_chat_model
from langchain.tools import tool
from langgraph.graph import StateGraph, MessagesState
from langgraph.prebuilt import Tool<PERSON>ode

@tool
def get_current_time() -> str:
    """Returns the current time in UTC."""
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")

@tool
def add_numbers(a: int, b: int) -> int:
    """Add two numbers and return the result."""
    return a + b

@tool
def multiply_numbers(a: int, b: int) -> int:
    """Multiply two numbers and return the result."""
    return a * b

# minimal model init helper used in some docs
from langchain.chat_models import init_chat_model
model = init_chat_model(model="llama3.1:latest", model_provider="ollama")

# Step 3: Bind the tool so the model knows its description
model_with_tools = model.bind_tools([get_current_time])

# Step 4: Define a simple graph
graph = StateGraph(MessagesState)

# Add a model node that generates tool calls
graph.add_node("model", model_with_tools)

# Add a ToolNode that can execute tools
tool_node = ToolNode(tools=[get_current_time])
graph.add_node("tools", tool_node)

# Connect nodes
# After model → tools, after tools → model (loop)
graph.add_edge("model", "tools")
graph.add_edge("tools", "model")

# Set the entry point
graph.set_entry_point("model")

# Step 5: Compile the graph
app = graph.compile()

# Step 6: Run the graph with a message
# for event in app.stream({"messages": ["What is the current time now?"]}):
#     print(event)

print("\nGraph run complete.")